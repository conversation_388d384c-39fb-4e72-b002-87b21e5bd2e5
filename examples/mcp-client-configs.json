{"cursor_local_stdio": {"description": "Cursor IDE with local stdio transport", "config": {"mcpServers": {"tarot": {"command": "node", "args": ["/path/to/tarot-mcp/dist/index.js"]}}}}, "cursor_local_http": {"description": "Cursor IDE with local HTTP transport", "config": {"mcpServers": {"tarot": {"url": "http://localhost:3000/mcp"}}}}, "cursor_remote_http": {"description": "Cursor IDE with remote HTTP transport", "config": {"mcpServers": {"tarot": {"url": "https://your-domain.com/mcp"}}}}, "claude_desktop": {"description": "Claude Desktop application", "config": {"mcpServers": {"tarot": {"command": "node", "args": ["/path/to/tarot-mcp/dist/index.js"]}}}}, "vs_code_local": {"description": "VS Code with local server", "config": {"mcp": {"servers": {"tarot": {"type": "stdio", "command": "node", "args": ["/path/to/tarot-mcp/dist/index.js"]}}}}}, "vs_code_http": {"description": "VS Code with HTTP server", "config": {"mcp": {"servers": {"tarot": {"type": "http", "url": "http://localhost:3000/mcp"}}}}}, "windsurf_local": {"description": "Windsurf with local server", "config": {"mcpServers": {"tarot": {"command": "node", "args": ["/path/to/tarot-mcp/dist/index.js"]}}}}, "windsurf_sse": {"description": "Windsurf with SSE transport", "config": {"mcpServers": {"tarot": {"serverUrl": "http://localhost:3000/sse"}}}}, "context7_style": {"description": "Context7-style configuration for compatibility", "config": {"mcpServers": {"tarot": {"command": "npx", "args": ["-y", "tarot-mcp-server@latest"]}}}}, "docker_compose": {"description": "Using with Docker Compose", "config": {"mcpServers": {"tarot": {"url": "http://localhost:3000/mcp"}}}, "docker_compose": {"version": "3.8", "services": {"tarot-mcp": {"image": "tarot-mcp:latest", "ports": ["3000:3000"], "environment": ["NODE_ENV=production"], "restart": "unless-stopped"}}}}}